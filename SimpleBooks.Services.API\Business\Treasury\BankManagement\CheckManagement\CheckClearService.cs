﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckClearService : SimpleBooksBaseService<CheckClearModel, CheckClearModel, CreateCheckClearViewModel, UpdateCheckClearViewModel>, ICheckClearService
    {
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckClearService(
            LocalSession session,
            IHttpClientFactory httpClientFactory,
            ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(session, httpClientFactory)
        {
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers()
        {
            try
            {
                var result = await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Issued.Value);
                if (result == null)
                    return ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>.Failure("Failed to retrieve check treasury vouchers.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>.Failure($"An error occurred while retrieving check treasury vouchers: {ex.Message}");
            }
        }
    }
}
