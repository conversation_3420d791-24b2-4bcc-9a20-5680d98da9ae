﻿namespace SimpleBooks.Services.API.Business.Treasury
{
    public class PaymentTermService : SimpleBooksBaseService<PaymentTermModel, PaymentTermModel, CreatePaymentTermViewModel, UpdatePaymentTermViewModel>, IPaymentTermService
    {
        public PaymentTermService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }

        [HttpRequestMethod("AddStandardAsync", HttpMethodEnum.POST, true)]
        public async Task<ServiceResult<PaymentTermStandardModel>> AddStandardAsync(CreatePaymentTermStandardViewModel model)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<PaymentTermStandardModel>>(this, model);
                if (result == null)
                    return ServiceResult<PaymentTermStandardModel>.Failure("Failed to add payment term standard.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermStandardModel>.Failure(ex.Message);
            }
        }

        [HttpRequestMethod("UpdateStandardAsync", HttpMethodEnum.PUT, true)]
        public async Task<ServiceResult<PaymentTermStandardModel>> UpdateStandardAsync(UpdatePaymentTermStandardViewModel model)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<PaymentTermStandardModel>>(this, model);
                if (result == null)
                    return ServiceResult<PaymentTermStandardModel>.Failure("Failed to update payment term standard.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermStandardModel>.Failure(ex.Message);
            }
        }

        [HttpRequestMethod("AddDateDrivenAsync", HttpMethodEnum.POST, true)]
        public async Task<ServiceResult<PaymentTermDateDrivenModel>> AddDateDrivenAsync(CreatePaymentTermDateDrivenViewModel model)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<PaymentTermDateDrivenModel>>(this, model);
                if (result == null)
                    return ServiceResult<PaymentTermDateDrivenModel>.Failure("Failed to add payment term date driven.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermDateDrivenModel>.Failure(ex.Message);
            }
        }

        [HttpRequestMethod("UpdateDateDrivenAsync", HttpMethodEnum.PUT, true)]
        public async Task<ServiceResult<PaymentTermDateDrivenModel>> UpdateDateDrivenAsync(UpdatePaymentTermDateDrivenViewModel model)
        {
            try
            {
                var result = await _httpClient.SendRequest<ServiceResult<PaymentTermDateDrivenModel>>(this, model);
                if (result == null)
                    return ServiceResult<PaymentTermDateDrivenModel>.Failure("Failed to update payment term date driven.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermDateDrivenModel>.Failure(ex.Message);
            }
        }

        [HttpRequestMethod(nameof(SelectivePaymentTermDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<PaymentTermDto>>>(this) ?? new List<PaymentTermDto>();
            return result;
        }

        [HttpRequestMethod(nameof(GetByIdJsonAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            string result = await _httpClient.SendRequest<ServiceResult<string>>(this, new { id }) ?? string.Empty;
            return result;
        }
    }
}
