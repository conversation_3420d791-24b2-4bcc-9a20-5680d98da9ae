﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class ProductTaxService : SimpleBooksBaseService<ProductTaxModel, ProductTaxModel, CreateProductTaxViewModel, UpdateProductTaxViewModel>, IProductTaxService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductTaxService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.ProductTax)
        {
            _unitOfWork = unitOfWork;
        }
    }
}
