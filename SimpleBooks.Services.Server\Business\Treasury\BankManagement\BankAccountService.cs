﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement
{
    public class BankAccountService : SimpleBooksBaseService<BankAccountModel, BankAccountModel, CreateBankAccountViewModel, UpdateBankAccountViewModel>, IBankAccountService
    {
        private readonly IUnitOfWork _unitOfWork;

        public BankAccountService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.BankAccount)
        {
            _unitOfWork = unitOfWork;
        }
    }
}
