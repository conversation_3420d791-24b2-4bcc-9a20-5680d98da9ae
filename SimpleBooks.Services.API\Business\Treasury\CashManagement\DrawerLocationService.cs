﻿namespace SimpleBooks.Services.API.Business.Treasury.CashManagement
{
    public class DrawerLocationService : SimpleBooksBaseService<DrawerLocationModel, DrawerLocationModel, CreateDrawerLocationViewModel, UpdateDrawerLocationViewModel>, IDrawerLocationService
    {
        public DrawerLocationService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }
    }
}
