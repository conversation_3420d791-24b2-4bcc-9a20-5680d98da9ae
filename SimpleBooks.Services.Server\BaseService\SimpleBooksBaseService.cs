﻿namespace SimpleBooks.Services.Server.BaseService
{
    public class SimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate> : BaseService<TEntity, TEntityView>, ISimpleBooksBaseService<TEntity, TEntityView, TEntityCreate, TEntityUpdate>
        where TEntity : class, IBaseIdentityModel
        where TEntityView : class, IBaseIdentityModel
        where TEntityCreate : BaseCreateViewModel, IEntityMapper<TEntity, TEntityCreate>
        where TEntityUpdate : BaseUpdateViewModel, IEntityMapper<TEntity, TEntityUpdate>
    {
        private Ulid? _employeeId;

        public SimpleBooksBaseService(IServiceProvider serviceProvider, IBaseRepository<TEntity, TEntityView> repository) : base(repository)
        {
            _employeeId = serviceProvider.GetEmployeeId();
        }

        public override void EditModelBeforeSave(TEntity model)
        {
            base.EditModelBeforeSave(model);

            MakeSurePrimaryIdIsSet(model);

            Type entityType = model.GetType();
            var properties = entityType.GetProperties();
            foreach (var property in properties)
            {
                if (property.PropertyType.IsGenericType)
                {
                    var genericTypeDefinition = property.PropertyType.GetGenericTypeDefinition();

                    if (typeof(IEnumerable<>).IsAssignableFrom(genericTypeDefinition) ||
                        typeof(ICollection<>).IsAssignableFrom(genericTypeDefinition))
                    {
                        var collection = property.GetValue(model) as IEnumerable<object>;
                        if (collection != null)
                        {
                            foreach (var item in collection)
                            {
                                MakeSurePrimaryIdIsSet(item);
                            }
                        }
                    }
                }
            }
        }

        #region Async
        public virtual async Task<ServiceResult<TEntity?>> AddAsync(TEntityCreate model)
        {
            TEntity entity = model.ToEntity();
            return await AddAsync(entity);
        }

        public virtual async Task<ServiceResult<TEntity?>> UpdateAsync(TEntityUpdate model)
        {
            TEntity entity = model.ToEntity();
            return await UpdateAsync(entity);
        }
        #endregion

        #region Sync
        public ServiceResult<TEntity?> Add(TEntityCreate model) => Task.Run(async () => await AddAsync(model)).GetAwaiter().GetResult();

        public ServiceResult<TEntity?> Update(TEntityUpdate model) => Task.Run(async () => await UpdateAsync(model)).GetAwaiter().GetResult();
        #endregion

        private void MakeSurePrimaryIdIsSet(object item)
        {
            var primaryIdProperty = item.GetType().GetProperty(nameof(BaseIdentityModel.Id));
            if (primaryIdProperty != null)
            {
                Ulid? currentPrimaryIdValue = primaryIdProperty.GetValue(item) as Ulid?;
                if (currentPrimaryIdValue != null)
                {
                    if (currentPrimaryIdValue == Ulid.Empty)
                        primaryIdProperty.SetValue(item, Ulid.NewUlid());
                }
            }
        }
    }
}
