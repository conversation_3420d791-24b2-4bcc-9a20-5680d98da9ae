﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckRejectService : SimpleBooksBaseService<CheckRejectModel, CheckRejectModel, CreateCheckRejectViewModel, UpdateCheckRejectViewModel>, ICheckRejectService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;
        private readonly ICheckVaultService _checkVaultService;
        private readonly ICheckVaultLocationService _checkVaultLocationService;

        public CheckRejectService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            ICheckTreasuryVoucherService checkTreasuryVoucherService,
            ICheckVaultService checkVaultService,
            ICheckVaultLocationService checkVaultLocationService) : base(serviceProvider, unitOfWork.CheckReject)
        {
            _unitOfWork = unitOfWork;
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
            _checkVaultService = checkVaultService;
            _checkVaultLocationService = checkVaultLocationService;
        }

        protected override Func<IQueryable<CheckRejectModel>, IIncludableQueryable<CheckRejectModel, object>>? Includes =>
            x => x
            .Include(xx => xx.CheckTreasuryVouchers)
            .Include(xx => xx.CheckStatusHistories);

        public override async void ValidateEntity(CheckRejectModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.CheckTreasuryVouchers == null || !model.CheckTreasuryVouchers.Any())
            {
                throw new ValidationException("At least one check treasury voucher must be provided for rejection.");
            }
            var checksIds = model.CheckTreasuryVouchers.Select(x => x.Id);
            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>()
            {
                SearchValue = x => checksIds.Contains(x.Id),
            };
            var checkTreasuryVouchers = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecifications);
            foreach (var checkTreasuryVoucher in checkTreasuryVouchers)
            {
                if (checkTreasuryVoucher.CheckStatusId != CheckStatusEnumeration.Deposited.Value)
                {
                    throw new ValidationException("All check treasury vouchers must be in 'Deposited' status for rejection.");
                }
            }
        }

        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCheckVaultListAsync() => await _checkVaultService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<CheckVaultLocationModel>>> SelectiveCheckVaultLocationListAsync() => await _checkVaultLocationService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers() => await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Deposited.Value);
    }
}
