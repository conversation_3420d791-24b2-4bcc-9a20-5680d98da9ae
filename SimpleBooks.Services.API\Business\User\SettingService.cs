﻿namespace SimpleBooks.Services.API.Business.User
{
    public class SettingService : SimpleBooksBaseService<SettingModel, SettingModel, CreateSettingViewModel, UpdateSettingViewModel>, ISettingService
    {
        public SettingService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }

        [HttpRequestMethod(nameof(GetCurrentSetting), HttpMethodEnum.GET)]
        public async Task<ServiceResult<SettingModel>> GetCurrentSetting()
            => await _httpClient.SendRequest<ServiceResult<SettingModel>>(this) ?? new SettingModel();
    }
}
