﻿namespace SimpleBooks.Services.Server.Business.Sales
{
    public class InvoiceReturnService : SimpleBooksBaseService<InvoiceReturnModel, IndexInvoiceReturnViewModel, CreateInvoiceReturnViewModel, UpdateInvoiceReturnViewModel>, IInvoiceReturnService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICustomerService _customerService;
        private readonly ICustomerTypeService _customerTypeService;
        private readonly IEmployeeService _employeeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;
        private readonly IStoreService _storeService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public InvoiceReturnService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            ICustomerService customerService,
            ICustomerTypeService customerTypeService,
            IEmployeeService employeeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService,
            IStoreService storeService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(serviceProvider, unitOfWork.InvoiceReturn)
        {
            _unitOfWork = unitOfWork;
            _customerService = customerService;
            _customerTypeService = customerTypeService;
            _employeeService = employeeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
            _storeService = storeService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        protected override Func<IQueryable<InvoiceReturnModel>, IIncludableQueryable<InvoiceReturnModel, object>>? Includes =>
            x => x
            .Include(xx => xx.Inventories).ThenInclude(xx => xx.InventoryTaxes);

        public override void EditModelBeforeSave(InvoiceReturnModel model)
        {
            base.EditModelBeforeSave(model);
            InventoryHelper.ArrangeInventory(model.Inventories, TransactionTypeEnumeration.InvoiceReturn, model.Id);
        }

        public override void ValidateEntity(InvoiceReturnModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            var inventoryErrors = InventoryHelper.ValidateInventory(model.Inventories, TransactionTypeEnumeration.InvoiceReturn);
            if (inventoryErrors.Length > 0)
                throw new ValidationException(string.Join(", ", inventoryErrors));
        }

        public async Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerListAsync() => await _customerService.SelectiveCustomerDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync() => await _customerTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync() => await _employeeService.GetRepEmployeeSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync() => await _storeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();
    }
}
