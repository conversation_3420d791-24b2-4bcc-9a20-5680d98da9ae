﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultService : SimpleBooksBaseService<CheckVaultModel, CheckVaultModel, CreateCheckVaultViewModel, UpdateCheckVaultViewModel>, ICheckVaultService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CheckVaultService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.CheckVault)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<CheckVaultModel>, IIncludableQueryable<CheckVaultModel, object>>? Includes =>
            x => x
            .Include(xx => xx.CheckVaultLocations);

        public override void EditModelBeforeSave(CheckVaultModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var item in model.CheckVaultLocations)
                item.CheckVaultId = model.Id;
        }

        public override void ValidateEntity(CheckVaultModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<CheckVaultModel> repositorySpecifications = new RepositorySpecifications<CheckVaultModel>()
                {
                    SearchValue = x => x.CheckVaultName == model.CheckVaultName,
                    IsTackable = false,
                };
                var isExistingCheckVault = _unitOfWork.CheckVault.Get(repositorySpecifications);
                if (isExistingCheckVault != null && isExistingCheckVault.Id != model.Id)
                    throw new ValidationException("Check vault with the same Name already exists.");
            }
            if (model.CheckVaultLocations.Count == 0)
                throw new ValidationException("At least one check vault location is required.");
            if (model.CheckVaultLocations.Count >= 1)
            {
                var hasDuplicates = model.CheckVaultLocations.GroupBy(x => x.CheckVaultLocationNumber).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Check vault locations must be unique.");
                var hasInvalidCheckVaultLocationNumber = model.CheckVaultLocations.Any(x => string.IsNullOrWhiteSpace(x.CheckVaultLocationNumber));
                if (hasInvalidCheckVaultLocationNumber)
                    throw new ValidationException("Check vault location name must not be empty.");
            }
        }
    }
}
