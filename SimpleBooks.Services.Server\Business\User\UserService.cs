﻿namespace SimpleBooks.Services.Server.Business.User
{
    public class UserService : SimpleBooksBaseService<UserModel, UserModel, CreateUserViewModel, UpdateUserViewModel>, IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly UserManager<UserModel> _userManager;

        public UserService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            UserManager<UserModel> userManager) : base(serviceProvider, unitOfWork.User)
        {
            _unitOfWork = unitOfWork;
            _userManager = userManager;
        }

        public override async Task<ServiceResult<UserModel?>> AddAsync(CreateUserViewModel model)
        {
            try
            {
                // Create user using Identity's UserManager for proper password hashing
                var user = new UserModel
                {
                    UserName = model.UserName,
                    Email = model.Email, // If you have email in your model
                    UserTypeId = model.UserTypeId,
                    ScreensAccessProfileId = model.ScreensAccessProfileId,
                    EmployeeId = model.EmployeeId,
                    SettingId = model.SettingId
                };

                // Use UserManager to create user with hashed password
                var result = await _userManager.CreateAsync(user, model.Password);

                if (result.Succeeded)
                {
                    return user;
                }
                else
                {
                    string errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return ServiceResult<UserModel?>.Failure($"Failed to create user: {errors}");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<UserModel?>.Failure($"Error creating user: {ex.Message}");
            }
        }

        public async Task<ServiceResult<bool>> ChangePasswordAsync(Ulid userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return ServiceResult<bool>.Failure("User not found");
                }

                var result = await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);

                if (result.Succeeded)
                {
                    return true;
                }
                else
                {
                    string errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return ServiceResult<bool>.Failure($"Failed to change password: {errors}");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"Error changing password: {ex.Message}");
            }
        }

        public async Task<ServiceResult<bool>> ResetPasswordAsync(Ulid userId, string newPassword)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    return ServiceResult<bool>.Failure("User not found");
                }

                // Remove current password and set new one
                var removeResult = await _userManager.RemovePasswordAsync(user);
                if (removeResult.Succeeded)
                {
                    var addResult = await _userManager.AddPasswordAsync(user, newPassword);
                    if (addResult.Succeeded)
                    {
                        return true;
                    }
                    else
                    {
                        string errors = string.Join(", ", addResult.Errors.Select(e => e.Description));
                        return ServiceResult<bool>.Failure($"Failed to set new password: {errors}");
                    }
                }
                else
                {
                    string errors = string.Join(", ", removeResult.Errors.Select(e => e.Description));
                    return ServiceResult<bool>.Failure($"Failed to remove current password: {errors}");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"Error resetting password: {ex.Message}");
            }
        }
    }
}
