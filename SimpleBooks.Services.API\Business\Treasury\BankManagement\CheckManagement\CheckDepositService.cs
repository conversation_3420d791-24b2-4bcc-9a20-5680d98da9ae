﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckDepositService : SimpleBooksBaseService<CheckDepositModel, CheckDepositModel, CreateCheckDepositViewModel, UpdateCheckDepositViewModel>, ICheckDepositService
    {
        private readonly IBankService _bankService;
        private readonly IBankAccountService _bankAccountService;
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckDepositService(
            LocalSession session,
            IHttpClientFactory httpClientFactory,
            IBankService bankService,
            IBankAccountService bankAccountService,
            ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(session, httpClientFactory)
        {
            _bankService = bankService;
            _bankAccountService = bankAccountService;
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBankListAsync() => await _bankService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<BankAccountModel>>> SelectiveBankAccountListAsync() => await _bankAccountService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers()
        {
            try
            {
                var result = await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Received.Value);
                if (result == null)
                    return ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>.Failure("Failed to retrieve check treasury vouchers.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>.Failure($"An error occurred while retrieving check treasury vouchers: {ex.Message}");
            }
        }
    }
}
