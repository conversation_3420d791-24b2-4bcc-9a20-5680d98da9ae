﻿namespace SimpleBooks.Services.API.Business.Treasury
{
    public class TreasuryLineService : SimpleBooksBaseService<TreasuryLineModel, TreasuryLineModel, CreateTreasuryLineViewModel, UpdateTreasuryLineViewModel>, ITreasuryLineService
    {
        public TreasuryLineService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }

        [HttpRequestMethod(nameof(SelectiveTreasuryLineDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<TreasuryLineDto>>> SelectiveTreasuryLineDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<TreasuryLineDto>>>(this) ?? new List<TreasuryLineDto>();
            return result;
        }

        [HttpRequestMethod(nameof(GetByIdJsonAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            string result = await _httpClient.SendRequest<string>(this, new { id }) ?? string.Empty;
            return result;
        }
    }
}
