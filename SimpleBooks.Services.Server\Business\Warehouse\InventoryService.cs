﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class InventoryService : SimpleBooksBaseService<InventoryModel, InventoryModel, CreateInventoryViewModel, UpdateInventoryViewModel>, IInventoryService
    {
        private readonly IUnitOfWork _unitOfWork;

        public InventoryService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.Inventory)
        {
            _unitOfWork = unitOfWork;
        }
    }
}
