﻿namespace SimpleBooks.Services.Server.Business.Purchases
{
    public class BillReturnService : SimpleBooksBaseService<BillReturnModel, IndexBillReturnViewModel, CreateBillReturnViewModel, UpdateBillReturnViewModel>, IBillReturnService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;
        private readonly IStoreService _storeService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public BillReturnService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService,
            IStoreService storeService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(serviceProvider, unitOfWork.BillReturn)
        {
            _unitOfWork = unitOfWork;
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
            _storeService = storeService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        protected override Func<IQueryable<BillReturnModel>, IIncludableQueryable<BillReturnModel, object>>? Includes =>
            x => x
            .Include(xx => xx.Inventories).ThenInclude(xx => xx.InventoryTaxes);

        public override void EditModelBeforeSave(BillReturnModel model)
        {
            base.EditModelBeforeSave(model);
            InventoryHelper.ArrangeInventory(model.Inventories, TransactionTypeEnumeration.BillReturn, model.Id);
        }

        public override void ValidateEntity(BillReturnModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            var inventoryErrors = InventoryHelper.ValidateInventory(model.Inventories, TransactionTypeEnumeration.BillReturn);
            if (inventoryErrors.Length > 0)
                throw new ValidationException(string.Join(", ", inventoryErrors));
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync() => await _storeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();
    }
}
