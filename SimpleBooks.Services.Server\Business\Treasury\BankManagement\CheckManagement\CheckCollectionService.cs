﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckCollectionService : SimpleBooksBaseService<CheckCollectionModel, CheckCollectionModel, CreateCheckCollectionViewModel, UpdateCheckCollectionViewModel>, ICheckCollectionService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckCollectionService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(serviceProvider, unitOfWork.CheckCollection)
        {
            _unitOfWork = unitOfWork;
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        protected override Func<IQueryable<CheckCollectionModel>, IIncludableQueryable<CheckCollectionModel, object>>? Includes =>
            x => x
            .Include(xx => xx.CheckTreasuryVouchers)
            .Include(xx => xx.CheckStatusHistories);

        public override async void ValidateEntity(CheckCollectionModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.CheckTreasuryVouchers == null || !model.CheckTreasuryVouchers.Any())
            {
                throw new ValidationException("At least one check treasury voucher must be provided for collection.");
            }
            var checksIds = model.CheckTreasuryVouchers.Select(x => x.Id);
            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>()
            {
                SearchValue = x => checksIds.Contains(x.Id),
            };
            var checkTreasuryVouchers = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecifications);
            foreach (var checkTreasuryVoucher in checkTreasuryVouchers)
            {
                if (checkTreasuryVoucher.CheckStatusId != CheckStatusEnumeration.Deposited.Value)
                {
                    throw new ValidationException("All check treasury vouchers must be in 'Deposited' status for collection.");
                }
            }
        }

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers() => await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Deposited.Value);
    }
}
