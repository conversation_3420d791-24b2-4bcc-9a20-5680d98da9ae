﻿namespace SimpleBooks.Services.Server.Business.Treasury.CashManagement
{
    public class DrawerLocationService : SimpleBooksBaseService<DrawerLocationModel, DrawerLocationModel, CreateDrawerLocationViewModel, UpdateDrawerLocationViewModel>, IDrawerLocationService
    {
        private readonly IUnitOfWork _unitOfWork;

        public DrawerLocationService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.DrawerLocation)
        {
            _unitOfWork = unitOfWork;
        }
    }
}
