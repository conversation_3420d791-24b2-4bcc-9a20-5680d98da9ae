﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckStatusHistoryService : SimpleBooksBaseService<CheckStatusHistoryModel, CheckStatusHistoryModel, CreateCheckStatusHistoryViewModel, UpdateCheckStatusHistoryViewModel>, ICheckStatusHistoryService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CheckStatusHistoryService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.CheckStatusHistory)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> TransitionCheckStatusAsync(Ulid checkId, Ulid status)
        {
            //RepositorySpecifications<TreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<TreasuryVoucherModel>()
            //{
            //    Includes = x => x.Include(xx => xx.CheckStatusHistories),
            //    SearchValue = x => x.Id == checkId,
            //};

            //var check = await _unitOfWork.TreasuryVoucher.GetAsync(repositorySpecifications);

            //if (check == null)
            //    throw new InvalidOperationException("Check not found.");

            //var direction = TransactionTypeEnumeration.FromValue(check.TransactionTypeId);
            //if (!TransactionTypeEnumeration.IsTreasuryRelated(check.TransactionTypeId))
            //    throw new InvalidOperationException("Invalid transaction type for check status transition.");
            //var currentStatus = CheckStatusEnumeration.FromValue(check.CheckStatusHistories.Last().CheckStatusId);
            //if (currentStatus == null)
            //    throw new InvalidOperationException("Current check status not found.");
            //var newStatus = CheckStatusEnumeration.FromValue(status);
            //if (newStatus == null)
            //    throw new InvalidOperationException("New check status not found.");
            //if (!IsValidTransition(check.TransactionTypeId, currentStatus.Value, newStatus.Value))
            //    throw new InvalidOperationException($"Invalid transition from {currentStatus.Name} to {newStatus.Name} for {direction.Name} check.");

            //var oldStatus = currentStatus;

            //Add(new CheckStatusHistoryModel
            //{
            //    TransactionDate = DateTime.Now,
            //    CheckStatusId = newStatus.Value,
            //});
            return true;
        }

        /// <summary>
        /// Defines allowed transitions for Issued and Received check directions.
        /// </summary>
        private bool IsValidTransition(Ulid transactionType, Ulid current, Ulid target)
        {
            if (transactionType == TransactionTypeEnumeration.TreasuryCheckOut.Value)
            {
                if (current == CheckStatusEnumeration.Issued.Value && (target == CheckStatusEnumeration.Collected.Value || target == CheckStatusEnumeration.Returned.Value))
                    return true;
            }

            if (transactionType == TransactionTypeEnumeration.TreasuryCheckIn.Value)
            {
                if (current == CheckStatusEnumeration.Received.Value && target == CheckStatusEnumeration.Deposited.Value)
                    return true;
                if (current == CheckStatusEnumeration.Deposited.Value && (target == CheckStatusEnumeration.Collected.Value || target == CheckStatusEnumeration.Rejected.Value))
                    return true;
                if (current == CheckStatusEnumeration.Rejected.Value && target == CheckStatusEnumeration.Returned.Value)
                    return true;
            }

            return false;
        }

    }
}
