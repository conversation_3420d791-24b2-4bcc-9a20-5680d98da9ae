﻿namespace SimpleBooks.Services.API.Business.Warehouse
{
    public class ProductCategoryService : SimpleBooksBaseService<ProductCategoryModel, ProductCategoryModel, CreateProductCategoryViewModel, UpdateProductCategoryViewModel>, IProductCategoryService
    {
        public ProductCategoryService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }
    }
}
