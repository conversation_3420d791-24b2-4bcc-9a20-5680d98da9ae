﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement
{
    public class BankAccountService : SimpleBooksBaseService<BankAccountModel, BankAccountModel, CreateBankAccountViewModel, UpdateBankAccountViewModel>, IBankAccountService
    {
        public BankAccountService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }
    }
}
