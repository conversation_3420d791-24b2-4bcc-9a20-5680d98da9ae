﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultLocationService : SimpleBooksBaseService<CheckVaultLocationModel, CheckVaultLocationModel, CreateCheckVaultLocationViewModel, UpdateCheckVaultLocationViewModel>, ICheckVaultLocationService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CheckVaultLocationService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.CheckVaultLocation)
        {
            _unitOfWork = unitOfWork;
        }
    }
}
