﻿namespace SimpleBooks.Services.Server.Business.Tax
{
    public class TaxSubTypeService : SimpleBooksBaseService<TaxSubTypeModel, TaxSubTypeModel, CreateTaxSubTypeViewModel, UpdateTaxSubTypeViewModel>, ITaxSubTypeService
    {
        private readonly IUnitOfWork _unitOfWork;

        public TaxSubTypeService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.TaxSubType)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<TaxSubTypeModel>, IIncludableQueryable<TaxSubTypeModel, object>>? Includes
            => x => x.Include(xx => xx.TaxType);

        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> SelectiveTaxSubTypeDtoListAsync()
        {
            try
            {
                var result = await _unitOfWork.TaxSubType.SelectiveTaxSubTypeDtoListAsync();
                return ServiceResult<IEnumerable<TaxSubTypeDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<TaxSubTypeDto>>.Failure(ex.Message);
            }
        }
    }
}
