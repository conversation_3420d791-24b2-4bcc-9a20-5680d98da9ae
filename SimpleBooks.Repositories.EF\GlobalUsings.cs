﻿global using GMCadiomCore.Models.Abstractions;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.Model;
global using GMCadiomCore.Models.ModelValidation.CustomAttribute;
global using GMCadiomCore.Repositories.EF.Factory;
global using GMCadiomCore.Repositories.Factory;
global using GMCadiomCore.Repositories.IFactory;
global using GMCadiomCore.Repositories.IRepository;
global using GMCadiomCore.Shared.Helper;
global using Microsoft.AspNetCore.Identity;
global using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.ChangeTracking;
global using Microsoft.EntityFrameworkCore.Design;
global using Microsoft.EntityFrameworkCore.Diagnostics;
global using Microsoft.EntityFrameworkCore.Metadata.Builders;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.Hosting;
global using SimpleBooks.Models.BaseModels;
global using SimpleBooks.Models.Comparers;
global using SimpleBooks.Models.Enumerations;
global using SimpleBooks.Models.Model;
global using SimpleBooks.Models.Model.HR;
global using SimpleBooks.Models.Model.Purchases;
global using SimpleBooks.Models.Model.Sales;
global using SimpleBooks.Models.Model.Tax;
global using SimpleBooks.Models.Model.Treasury;
global using SimpleBooks.Models.Model.Treasury.BankManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Models.Model.Treasury.CashManagement;
global using SimpleBooks.Models.Model.User;
global using SimpleBooks.Models.Model.Warehouse;
global using SimpleBooks.Models.ModelDTO.Audit;
global using SimpleBooks.Models.ModelDTO.Authentication;
global using SimpleBooks.Models.ModelDTO.Purchases;
global using SimpleBooks.Models.ModelDTO.Sales;
global using SimpleBooks.Models.ModelDTO.Tax;
global using SimpleBooks.Models.ModelMapper.Tax;
global using SimpleBooks.Models.ModelMapper.Warehouse;
global using SimpleBooks.Models.ViewModel.Purchases.Bill;
global using SimpleBooks.Models.ViewModel.Purchases.BillReturn;
global using SimpleBooks.Models.ViewModel.Purchases.PurchaseOrder;
global using SimpleBooks.Models.ViewModel.Purchases.Vendor;
global using SimpleBooks.Models.ViewModel.Sales.Customer;
global using SimpleBooks.Models.ViewModel.Sales.Invoice;
global using SimpleBooks.Models.ViewModel.Sales.InvoiceReturn;
global using SimpleBooks.Models.ViewModel.Sales.SalesOrder;
global using SimpleBooks.Models.ViewModel.Warehouse.Product;
global using SimpleBooks.PermissionAndSession;
global using SimpleBooks.PermissionAndSession.Authentication.Extensions;
global using SimpleBooks.Repositories.Core.IFactory;
global using SimpleBooks.Repositories.Core.IRepository.Purchases;
global using SimpleBooks.Repositories.Core.IRepository.Sales;
global using SimpleBooks.Repositories.Core.IRepository.Tax;
global using SimpleBooks.Repositories.Core.IRepository.Treasury;
global using SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Repositories.Core.IRepository.Treasury.CashManagement;
global using SimpleBooks.Repositories.Core.IRepository.User;
global using SimpleBooks.Repositories.EF.BaseRepository;
global using SimpleBooks.Repositories.EF.Factory.ConstLinqQueries;
global using SimpleBooks.Repositories.EF.Factory.Contexts;
global using SimpleBooks.Repositories.EF.Factory.DatabaseManager;
global using SimpleBooks.Repositories.EF.Repository;
global using SimpleBooks.Repositories.EF.Repository.HR;
global using SimpleBooks.Repositories.EF.Repository.Purchases;
global using SimpleBooks.Repositories.EF.Repository.Sales;
global using SimpleBooks.Repositories.EF.Repository.Tax;
global using SimpleBooks.Repositories.EF.Repository.Treasury;
global using SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement;
global using SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Repositories.EF.Repository.Treasury.CashManagement;
global using SimpleBooks.Repositories.EF.Repository.User;
global using SimpleBooks.Repositories.EF.Repository.Warehouse;
global using SimpleBooks.Shared.Helper;
global using System.Data;
global using System.Linq.Expressions;
global using System.Runtime.CompilerServices;
global using System.Text;
