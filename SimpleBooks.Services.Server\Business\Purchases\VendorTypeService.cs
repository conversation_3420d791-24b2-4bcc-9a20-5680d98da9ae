﻿namespace SimpleBooks.Services.Server.Business.Purchases
{
    public class VendorTypeService : SimpleBooksBaseService<VendorTypeModel, VendorTypeModel, CreateVendorTypeViewModel, UpdateVendorTypeViewModel>, IVendorTypeService
    {
        private readonly IUnitOfWork _unitOfWork;

        public VendorTypeService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.VendorType)
        {
            _unitOfWork = unitOfWork;
        }

        public override void ValidateEntity(VendorTypeModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<VendorTypeModel> repositorySpecifications = new RepositorySpecifications<VendorTypeModel>()
                {
                    SearchValue = x => x.VendorTypeName == model.VendorTypeName,
                    IsTackable = false,
                };
                var isExistingVendorType = _unitOfWork.VendorType.Get(repositorySpecifications);
                if (isExistingVendorType != null && isExistingVendorType.Id != model.Id)
                    throw new ValidationException("Vendor Type with the same Name already exists.");
            }
        }
    }
}
