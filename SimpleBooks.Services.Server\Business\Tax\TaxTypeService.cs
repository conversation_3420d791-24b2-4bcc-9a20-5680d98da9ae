﻿namespace SimpleBooks.Services.Server.Business.Tax
{
    public class TaxTypeService : SimpleBooksBaseService<TaxTypeModel, TaxTypeModel, CreateTaxTypeViewModel, UpdateTaxTypeViewModel>, ITaxTypeService
    {
        private readonly IUnitOfWork _unitOfWork;

        public TaxTypeService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.TaxType)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<TaxTypeModel>, IIncludableQueryable<TaxTypeModel, object>>? Includes
            => x => x.Include(xx => xx.TaxSubTypes);

        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> SelectiveTaxTypeDtoListAsync()
        {
            try
            {
                var result = await _unitOfWork.TaxType.SelectiveTaxTypeDtoListAsync();
                return ServiceResult<IEnumerable<TaxTypeDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<TaxTypeDto>>.Failure(ex.Message);
            }
        }
    }
}
