﻿namespace SimpleBooks.Services.API.Business.HR
{
    public class EmployeeService : SimpleBooksBaseService<EmployeeModel, EmployeeModel, CreateEmployeeViewModel, UpdateEmployeeViewModel>, IEmployeeService
    {
        public EmployeeService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }


        [HttpRequestMethod(nameof(GetRepEmployeeSelectListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<IdAndName>>> GetRepEmployeeSelectListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<IdAndName>>>(this) ?? new List<IdAndName>();
            return result;
        }
    }
}
