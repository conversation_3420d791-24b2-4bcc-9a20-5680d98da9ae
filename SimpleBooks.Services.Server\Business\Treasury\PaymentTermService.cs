﻿namespace SimpleBooks.Services.Server.Business.Treasury
{
    public class PaymentTermService : SimpleBooksBaseService<PaymentTermModel, PaymentTermModel, CreatePaymentTermViewModel, UpdatePaymentTermViewModel>, IPaymentTermService
    {
        private readonly IUnitOfWork _unitOfWork;

        public PaymentTermService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.PaymentTerm)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ServiceResult<PaymentTermStandardModel>> AddStandardAsync(CreatePaymentTermStandardViewModel model)
        {
            try
            {
                PaymentTermStandardModel entity = model.ToEntity();
                var result = await _unitOfWork.PaymentTerm.AddAsync(entity);
                if (result == null)
                    return ServiceResult<PaymentTermStandardModel>.Failure("Failed to add payment term standard.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermStandardModel>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<PaymentTermStandardModel>> UpdateStandardAsync(UpdatePaymentTermStandardViewModel model)
        {
            try
            {
                PaymentTermStandardModel entity = model.ToEntity();
                var result = await _unitOfWork.PaymentTerm.UpdateAsync(entity);
                if (result == null)
                    return ServiceResult<PaymentTermStandardModel>.Failure("Failed to update payment term standard.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermStandardModel>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<PaymentTermDateDrivenModel>> AddDateDrivenAsync(CreatePaymentTermDateDrivenViewModel model)
        {
            try
            {
                PaymentTermDateDrivenModel entity = model.ToEntity();
                var result = await _unitOfWork.PaymentTerm.AddAsync(entity);
                if (result == null)
                    return ServiceResult<PaymentTermDateDrivenModel>.Failure("Failed to add payment term date driven.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermDateDrivenModel>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<PaymentTermDateDrivenModel>> UpdateDateDrivenAsync(UpdatePaymentTermDateDrivenViewModel model)
        {
            try
            {
                PaymentTermDateDrivenModel entity = model.ToEntity();
                var result = await _unitOfWork.PaymentTerm.UpdateAsync(entity);
                if (result == null)
                    return ServiceResult<PaymentTermDateDrivenModel>.Failure("Failed to update payment term date driven.");
                return result;
            }
            catch (Exception ex)
            {
                return ServiceResult<PaymentTermDateDrivenModel>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermDtoListAsync() => (await GetAllAsync()).Data?.Select(x => new PaymentTermDto()
        {
            Id = x.Id,
            PaymentTermName = x.PaymentTermName,
            PaymentTermType = GetPaymentTermType(x).ToString(),
            PaymentTermJson = JsonConvert.SerializeObject(x, new PaymentTermConverter()),
        }).ToList() ?? new List<PaymentTermDto>();

        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            RepositorySpecifications<PaymentTermModel> repositorySpecifications = new RepositorySpecifications<PaymentTermModel>()
            {
                SearchValue = x => x.Id == id,
                IsTackable = false,
            };
            if (Includes is not null)
                repositorySpecifications.Includes = Includes;
            var model = await _unitOfWork.PaymentTerm.GetAsync(repositorySpecifications);
            if (model is null)
                return string.Empty;

            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                Converters = new List<JsonConverter> { new PaymentTermConverter() },
            };
            string json = JsonConvert.SerializeObject(model, jsonSerializerSettings);

            return json;
        }

        private Enums.PaymentTermType GetPaymentTermType(PaymentTermModel paymentTerm)
        {
            switch (paymentTerm)
            {
                case PaymentTermStandardModel:
                    return Enums.PaymentTermType.Standard;
                case PaymentTermDateDrivenModel:
                    return Enums.PaymentTermType.DateDriven;
                default:
                    throw new InvalidOperationException("Unknown payment term type.");
            }
        }
    }
}
