﻿namespace SimpleBooks.Services.Server.Business.Purchases
{
    public class BillService : SimpleBooksBaseService<BillModel, IndexBillViewModel, CreateBillViewModel, UpdateBillViewModel>, IBillService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;
        private readonly IStoreService _storeService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public BillService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService,
            IStoreService storeService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(serviceProvider, unitOfWork.Bill)
        {
            _unitOfWork = unitOfWork;
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
            _storeService = storeService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        protected override Func<IQueryable<BillModel>, IIncludableQueryable<BillModel, object>>? Includes =>
            x => x
            .Include(xx => xx.Inventories).ThenInclude(xx => xx.InventoryTaxes);

        public override void EditModelBeforeSave(BillModel model)
        {
            base.EditModelBeforeSave(model);
            InventoryHelper.ArrangeInventory(model.Inventories, TransactionTypeEnumeration.Bill, model.Id);
        }

        public override void ValidateEntity(BillModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            var inventoryErrors = InventoryHelper.ValidateInventory(model.Inventories, TransactionTypeEnumeration.Bill);
            if (inventoryErrors.Length > 0)
                throw new ValidationException(string.Join(", ", inventoryErrors));
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync() => await _storeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();

        public async Task<ServiceResult<IEnumerable<OpenPurchaseOrderDto>>> GetOpenPurchaseOrdersByVendorAsync(Ulid vendorId)
        {
            try
            {
                var result = await _unitOfWork.PurchaseOrder.GetOpenPurchaseOrdersByVendorAsync(vendorId);
                if (result == null)
                    return ServiceResult<IEnumerable<OpenPurchaseOrderDto>>.Failure("Purchase order not found.");
                return ServiceResult<IEnumerable<OpenPurchaseOrderDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenPurchaseOrderDto>>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>> GetOpenPurchaseOrderLinesAsync(Ulid purchaseOrderId)
        {
            try
            {
                var result = await _unitOfWork.PurchaseOrder.GetOpenPurchaseOrderLinesAsync(purchaseOrderId);
                if (result == null)
                    return ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>.Failure("Purchase order not found.");
                return ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>.Failure(ex.Message);
            }
        }
    }
}
