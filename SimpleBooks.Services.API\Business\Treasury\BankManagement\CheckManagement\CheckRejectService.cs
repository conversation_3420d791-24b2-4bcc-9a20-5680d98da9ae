﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckRejectService : SimpleBooksBaseService<CheckRejectModel, CheckRejectModel, CreateCheckRejectViewModel, UpdateCheckRejectViewModel>, ICheckRejectService
    {
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;
        private readonly ICheckVaultService _checkVaultService;
        private readonly ICheckVaultLocationService _checkVaultLocationService;

        public CheckRejectService(
            LocalSession session,
            IHttpClientFactory httpClientFactory,
            ICheckTreasuryVoucherService checkTreasuryVoucherService,
            ICheckVaultService checkVaultService,
            ICheckVaultLocationService checkVaultLocationService) : base(session, httpClientFactory)
        {
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
            _checkVaultService = checkVaultService;
            _checkVaultLocationService = checkVaultLocationService;
        }

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers() => await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Deposited.Value);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCheckVaultListAsync() => await _checkVaultService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<CheckVaultLocationModel>>> SelectiveCheckVaultLocationListAsync() => await _checkVaultLocationService.GetAllAsync();
    }
}
