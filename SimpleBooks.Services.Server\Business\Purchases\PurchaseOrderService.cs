﻿namespace SimpleBooks.Services.Server.Business.Purchases
{
    public class PurchaseOrderService : SimpleBooksBaseService<PurchaseOrderModel, IndexPurchaseOrderViewModel, CreatePurchaseOrderViewModel, UpdatePurchaseOrderViewModel>, IPurchaseOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;

        public PurchaseOrderService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService) : base(serviceProvider, unitOfWork.PurchaseOrder)
        {
            _unitOfWork = unitOfWork;
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
        }

        protected override Func<IQueryable<PurchaseOrderModel>, IIncludableQueryable<PurchaseOrderModel, object>>? Includes =>
            x => x
            .Include(xx => xx.PurchaseOrderLines);

        public override void EditModelBeforeSave(PurchaseOrderModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var purchaseOrderLine in model.PurchaseOrderLines)
            {
                decimal amount = 0;
                purchaseOrderLine.PurchaseOrderId = model.Id;
                decimal discountAmount = 0;
                if (purchaseOrderLine.IsPercentageDiscount && purchaseOrderLine.DiscountRate > 1)
                    purchaseOrderLine.DiscountRate = purchaseOrderLine.DiscountRate / 100;
                amount = purchaseOrderLine.Quantity * purchaseOrderLine.Price;
                if (purchaseOrderLine.IsPercentageDiscount)
                    discountAmount = amount * purchaseOrderLine.DiscountRate;
                else
                    discountAmount = purchaseOrderLine.DiscountRate;
                purchaseOrderLine.Amount = amount - discountAmount;
            }
        }

        public override void ValidateEntity(PurchaseOrderModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.PurchaseOrderLines.Count == 0)
                throw new ValidationException("At least one inventory item is required.");
            if (model.PurchaseOrderLines.Count >= 1)
            {
                var hasDuplicates = model.PurchaseOrderLines.GroupBy(x => x.ProductId).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Purchase order line items must be unique.");
                var hasInvalidQuantity = model.PurchaseOrderLines.Any(x => x.Quantity <= 0);
                if (hasInvalidQuantity)
                    throw new ValidationException("Purchase order line quantity must be greater than zero.");
                var hasInvalidCostPrice = model.PurchaseOrderLines.Any(x => x.Price < 0);
                if (hasInvalidCostPrice)
                    throw new ValidationException("Purchase order line cost price must be greater than or equal to zero.");
                if (model.PurchaseOrderLines.Any(x => x.IsPercentageDiscount && x.DiscountRate > 1))
                    throw new ValidationException("Purchase order line discount rate must be less than or equal to 1 when percentage discount is applied.");
                if (model.PurchaseOrderLines.Any(x => x.DiscountRate < 0))
                    throw new ValidationException("Purchase order line discount rate must be greater than or equal to zero.");
            }
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();

        public async Task<ServiceResult<Dictionary<string, decimal>>> GetBilledQuantitiesAsync(Ulid purchaseOrderId)
        {
            try
            {
                var result = await _unitOfWork.PurchaseOrder.GetBilledQuantitiesAsync(purchaseOrderId);
                return ServiceResult<Dictionary<string, decimal>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<Dictionary<string, decimal>>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<IEnumerable<BillDetailsDto>>> GetBillDetailsByOrderAndProductAsync(Ulid purchaseOrderId, Ulid productId)
        {
            try
            {
                var result = await _unitOfWork.PurchaseOrder.GetBillDetailsByOrderAndProductAsync(purchaseOrderId, productId);
                return ServiceResult<IEnumerable<BillDetailsDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<BillDetailsDto>>.Failure(ex.Message);
            }
        }
    }
}
