﻿namespace SimpleBooks.Services.Server.Business.Treasury.CashManagement
{
    public class DrawerService : SimpleBooksBaseService<DrawerModel, DrawerModel, CreateDrawerViewModel, UpdateDrawerViewModel>, IDrawerService
    {
        private readonly IUnitOfWork _unitOfWork;

        public DrawerService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.Drawer)
        {
            _unitOfWork = unitOfWork;
        }

        protected override Func<IQueryable<DrawerModel>, IIncludableQueryable<DrawerModel, object>>? Includes =>
            x => x
            .Include(xx => xx.DrawerLocations);

        public override void EditModelBeforeSave(DrawerModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var item in model.DrawerLocations)
                item.DrawerId = model.Id;
        }

        public override void ValidateEntity(DrawerModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<DrawerModel> repositorySpecifications = new RepositorySpecifications<DrawerModel>()
                {
                    SearchValue = x => x.DrawerName == model.DrawerName,
                    IsTackable = false,
                };
                var isExistingDrawer = _unitOfWork.Drawer.Get(repositorySpecifications);
                if (isExistingDrawer != null && isExistingDrawer.Id != model.Id)
                    throw new ValidationException("Drawer with the same Name already exists.");
            }
            if (model.DrawerLocations.Count == 0)
                throw new ValidationException("At least one drawer location is required.");
            if (model.DrawerLocations.Count >= 1)
            {
                var hasDuplicates = model.DrawerLocations.GroupBy(x => x.DrawerLocationNumber).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Drawer locations must be unique.");
                var hasInvalidDrawerLocationNumber = model.DrawerLocations.Any(x => string.IsNullOrWhiteSpace(x.DrawerLocationNumber));
                if (hasInvalidDrawerLocationNumber)
                    throw new ValidationException("Drawer location name must not be empty.");
            }
        }
    }
}
