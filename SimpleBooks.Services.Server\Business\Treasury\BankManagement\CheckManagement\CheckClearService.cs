﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckClearService : SimpleBooksBaseService<CheckClearModel, CheckClearModel, CreateCheckClearViewModel, UpdateCheckClearViewModel>, ICheckClearService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckClearService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(serviceProvider, unitOfWork.CheckClear)
        {
            _unitOfWork = unitOfWork;
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        protected override Func<IQueryable<CheckClearModel>, IIncludableQueryable<CheckClearModel, object>>? Includes =>
            x => x
            .Include(xx => xx.CheckTreasuryVouchers)
            .Include(xx => xx.CheckStatusHistories);

        public override async void ValidateEntity(CheckClearModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.CheckTreasuryVouchers == null || !model.CheckTreasuryVouchers.Any())
            {
                throw new ValidationException("At least one check treasury voucher must be provided for clear.");
            }
            var checksIds = model.CheckTreasuryVouchers.Select(x => x.Id);
            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>()
            {
                SearchValue = x => checksIds.Contains(x.Id),
            };
            var checkTreasuryVouchers = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecifications);
            foreach (var checkTreasuryVoucher in checkTreasuryVouchers)
            {
                if (checkTreasuryVoucher.CheckStatusId != CheckStatusEnumeration.Issued.Value)
                {
                    throw new ValidationException("All check treasury vouchers must be in 'Issued' status for clear.");
                }
            }
        }

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers() => await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Deposited.Value);
    }
}
