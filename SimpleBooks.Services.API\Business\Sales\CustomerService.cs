﻿namespace SimpleBooks.Services.API.Business.Sales
{
    public class CustomerService : SimpleBooksBaseService<CustomerModel, IndexCustomerViewModel, CreateCustomerViewModel, UpdateCustomerViewModel>, ICustomerService
    {
        private readonly ICustomerTypeService _customerTypeService;
        private readonly IEmployeeService _employeeService;
        private readonly IPaymentTermService _paymentTermService;

        public CustomerService(
            LocalSession session,
            IHttpClientFactory httpClientFactory,
            ICustomerTypeService customerTypeService,
            IEmployeeService employeeService,
            IPaymentTermService paymentTermService) : base(session, httpClientFactory)
        {
            _customerTypeService = customerTypeService;
            _employeeService = employeeService;
            _paymentTermService = paymentTermService;
        }

        [HttpRequestMethod(nameof(SelectiveCustomerDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<CustomerDto>>>(this) ?? new List<CustomerDto>();
            return result;
        }

        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync() => await _customerTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync() => await _employeeService.GetRepEmployeeSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectivePaymentTermListAsync() => await _paymentTermService.GetSelectListAsync();
    }
}
