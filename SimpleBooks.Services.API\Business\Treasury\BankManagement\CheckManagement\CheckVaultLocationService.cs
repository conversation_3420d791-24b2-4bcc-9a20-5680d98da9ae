﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultLocationService : SimpleBooksBaseService<CheckVaultLocationModel, CheckVaultLocationModel, CreateCheckVaultLocationViewModel, UpdateCheckVaultLocationViewModel>, ICheckVaultLocationService
    {
        public CheckVaultLocationService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }
    }
}
