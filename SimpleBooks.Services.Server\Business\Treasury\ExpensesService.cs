﻿namespace SimpleBooks.Services.Server.Business.Treasury
{
    public class ExpensesService : SimpleBooksBaseService<ExpensesModel, ExpensesModel, CreateExpensesViewModel, UpdateExpensesViewModel>, IExpensesService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ExpensesService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.Expenses)
        {
            _unitOfWork = unitOfWork;
        }

        public override void ValidateEntity(ExpensesModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<ExpensesModel> repositorySpecifications = new RepositorySpecifications<ExpensesModel>()
                {
                    SearchValue = x => x.ExpensesName == model.ExpensesName,
                    IsTackable = false,
                };
                var isExistingExpense = _unitOfWork.Expenses.Get(repositorySpecifications);
                if (isExistingExpense != null && isExistingExpense.Id != model.Id)
                    throw new ValidationException("Expense with the same Name already exists.");
            }
        }
    }
}
