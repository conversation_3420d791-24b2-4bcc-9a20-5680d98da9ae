﻿namespace SimpleBooks.Services.Server.Business.Treasury
{
    public class TreasuryLineService : SimpleBooksBaseService<TreasuryLineModel, TreasuryLineModel, CreateTreasuryLineViewModel, UpdateTreasuryLineViewModel>, ITreasuryLineService
    {
        private readonly IUnitOfWork _unitOfWork;

        public TreasuryLineService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.TreasuryLine)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ServiceResult<IEnumerable<TreasuryLineDto>>> SelectiveTreasuryLineDtoListAsync() => (await GetAllAsync()).Data?.Select(x => new TreasuryLineDto()
        {
            Id = x.Id,
            Amount = x.Amount,
            TreasuryLineType = TreasuryLineTypeEnumeration.FromValue(x.TreasuryLineTypeId).Name,
            TreasuryLineJson = JsonConvert.SerializeObject(x),
        }).ToList() ?? new List<TreasuryLineDto>();

        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            RepositorySpecifications<TreasuryLineModel> repositorySpecifications = new RepositorySpecifications<TreasuryLineModel>()
            {
                SearchValue = x => x.Id == id,
            };
            var model = await _unitOfWork.TreasuryLine.GetAsync(repositorySpecifications);
            if (model is null)
                return string.Empty;

            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                PreserveReferencesHandling = PreserveReferencesHandling.Objects,
            };
            string json = JsonConvert.SerializeObject(model, jsonSerializerSettings);

            return JsonConvert.SerializeObject(json);
        }
    }
}
