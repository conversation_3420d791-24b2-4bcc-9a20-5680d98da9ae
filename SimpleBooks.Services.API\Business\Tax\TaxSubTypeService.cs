﻿namespace SimpleBooks.Services.API.Business.Tax
{
    public class TaxSubTypeService : SimpleBooksBaseService<TaxSubTypeModel, TaxSubTypeModel, CreateTaxSubTypeViewModel, UpdateTaxSubTypeViewModel>, ITaxSubTypeService
    {
        public TaxSubTypeService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }

        [HttpRequestMethod(nameof(SelectiveTaxSubTypeDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> SelectiveTaxSubTypeDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<TaxSubTypeDto>>>(this) ?? new List<TaxSubTypeDto>();
            return result;
        }
    }
}
