﻿namespace SimpleBooks.Services.API.Business.Warehouse
{
    public class ProductService : SimpleBooksBaseService<ProductModel, IndexProductViewModel, CreateProductViewModel, UpdateProductViewModel>, IProductService
    {
        private readonly IProductCategoryService _productCategoryService;
        private readonly IUnitService _unitService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public ProductService(
            LocalSession session,
            IHttpClientFactory httpClientFactory,
            IProductCategoryService productCategoryService,
            IUnitService unitService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(session, httpClientFactory)
        {
            _productCategoryService = productCategoryService;
            _unitService = unitService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        public async Task<ServiceResult<IEnumerable<ProductTypeEnumeration>>> SelectiveProductTypeListAsync() => await Task.FromResult(ProductTypeEnumeration.ProductTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveProductCategoryListAsync() => await _productCategoryService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();
    }
}
