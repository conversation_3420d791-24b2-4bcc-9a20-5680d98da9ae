﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class UnitService : SimpleBooksBaseService<UnitModel, UnitModel, CreateUnitViewModel, UpdateUnitViewModel>, IUnitService
    {
        private readonly IUnitOfWork _unitOfWork;

        public UnitService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.Unit)
        {
            _unitOfWork = unitOfWork;
        }

        public override void ValidateEntity(UnitModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<UnitModel> repositorySpecifications = new RepositorySpecifications<UnitModel>()
                {
                    SearchValue = x => x.UnitName == model.UnitName,
                    IsTackable = false,
                };
                var isExistingUnit = _unitOfWork.Unit.Get(repositorySpecifications);
                if (isExistingUnit != null && isExistingUnit.Id != model.Id)
                    throw new ValidationException("Unit with the same Name already exists.");
            }
        }
    }
}
