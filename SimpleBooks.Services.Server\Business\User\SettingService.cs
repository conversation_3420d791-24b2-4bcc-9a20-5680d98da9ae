﻿namespace SimpleBooks.Services.Server.Business.User
{
    public class SettingService : SimpleBooksBaseService<SettingModel, SettingModel, CreateSettingViewModel, UpdateSettingViewModel>, ISettingService
    {
        private readonly IUnitOfWork _unitOfWork;

        public SettingService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.Setting)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ServiceResult<SettingModel>> GetCurrentSetting()
            => await _unitOfWork.Setting.GetAsync(new RepositorySpecifications<SettingModel>() { SearchValue = x => x.Id == Ulid.Parse("01JM898XB2RH67PPFANBQV0KRG"), }) ??
            new SettingModel();
    }
}
