﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class ProductUnitService : SimpleBooksBaseService<ProductUnitModel, ProductUnitModel, CreateProductUnitViewModel, UpdateProductUnitViewModel>, IProductUnitService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ProductUnitService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.ProductUnit)
        {
            _unitOfWork = unitOfWork;
        }
    }
}
