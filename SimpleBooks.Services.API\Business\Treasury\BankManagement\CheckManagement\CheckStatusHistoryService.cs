﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckStatusHistoryService : SimpleBooksBaseService<CheckStatusHistoryModel, CheckStatusHistoryModel, CreateCheckStatusHistoryViewModel, UpdateCheckStatusHistoryViewModel>, ICheckStatusHistoryService
    {
        public CheckStatusHistoryService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }
    }
}
