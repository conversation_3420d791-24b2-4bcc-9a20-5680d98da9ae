﻿namespace SimpleBooks.Services.Server.Business.Sales
{
    public class SalesOrderService : SimpleBooksBaseService<SalesOrderModel, IndexSalesOrderViewModel, CreateSalesOrderViewModel, UpdateSalesOrderViewModel>, ISalesOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICustomerService _customerService;
        private readonly ICustomerTypeService _customerTypeService;
        private readonly IEmployeeService _employeeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;

        public SalesOrderService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            ICustomerService customerService,
            ICustomerTypeService customerTypeService,
            IEmployeeService employeeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService) : base(serviceProvider, unitOfWork.SalesOrder)
        {
            _unitOfWork = unitOfWork;
            _customerService = customerService;
            _customerTypeService = customerTypeService;
            _employeeService = employeeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
        }

        protected override Func<IQueryable<SalesOrderModel>, IIncludableQueryable<SalesOrderModel, object>>? Includes =>
            x => x
            .Include(xx => xx.SalesOrderLines);

        public override void EditModelBeforeSave(SalesOrderModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var salesOrderLine in model.SalesOrderLines)
            {
                decimal amount = 0;
                salesOrderLine.SalesOrderId = model.Id;
                decimal discountAmount = 0;
                if (salesOrderLine.IsPercentageDiscount && salesOrderLine.DiscountRate > 1)
                    salesOrderLine.DiscountRate = salesOrderLine.DiscountRate / 100;
                amount = salesOrderLine.Quantity * salesOrderLine.Price;
                if (salesOrderLine.IsPercentageDiscount)
                    discountAmount = amount * salesOrderLine.DiscountRate;
                else
                    discountAmount = salesOrderLine.DiscountRate;
                salesOrderLine.Amount = amount - discountAmount;
            }
        }

        public override void ValidateEntity(SalesOrderModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.SalesOrderLines.Count == 0)
                throw new ValidationException("At least one inventory item is required.");
            if (model.SalesOrderLines.Count >= 1)
            {
                var hasDuplicates = model.SalesOrderLines.GroupBy(x => x.ProductId).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Sales order line items must be unique.");
                var hasInvalidQuantity = model.SalesOrderLines.Any(x => x.Quantity <= 0);
                if (hasInvalidQuantity)
                    throw new ValidationException("Sales order line quantity must be greater than zero.");
                var hasInvalidCostPrice = model.SalesOrderLines.Any(x => x.Price < 0);
                if (hasInvalidCostPrice)
                    throw new ValidationException("Sales order line cost price must be greater than or equal to zero.");
                if (model.SalesOrderLines.Any(x => x.IsPercentageDiscount && x.DiscountRate > 1))
                    throw new ValidationException("Sales order line discount rate must be less than or equal to 1 when percentage discount is applied.");
                if (model.SalesOrderLines.Any(x => x.DiscountRate < 0))
                    throw new ValidationException("Sales order line discount rate must be greater than or equal to zero.");
            }
        }

        public async Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerListAsync() => await _customerService.SelectiveCustomerDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync() => await _customerTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync() => await _employeeService.GetRepEmployeeSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();

        public async Task<ServiceResult<Dictionary<string, decimal>>> GetInvoicedQuantitiesAsync(Ulid salesOrderId)
        {
            try
            {
                var result = await _unitOfWork.SalesOrder.GetInvoicedQuantitiesAsync(salesOrderId);
                return ServiceResult<Dictionary<string, decimal>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<Dictionary<string, decimal>>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<IEnumerable<InvoiceDetailsDto>>> GetInvoiceDetailsByOrderAndProductAsync(Ulid salesOrderId, Ulid productId)
        {
            try
            {
                var result = await _unitOfWork.SalesOrder.GetInvoiceDetailsByOrderAndProductAsync(salesOrderId, productId);
                return ServiceResult<IEnumerable<InvoiceDetailsDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<InvoiceDetailsDto>>.Failure(ex.Message);
            }
        }
    }
}
