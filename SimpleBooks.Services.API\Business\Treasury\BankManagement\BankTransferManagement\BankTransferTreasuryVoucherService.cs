﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.BankTransferManagement
{
    public class BankTransferTreasuryVoucherService : SimpleBooksBaseService<BankTransferTreasuryVoucherModel, BankTransferTreasuryVoucherModel, CreateBankTransferTreasuryVoucherViewModel, UpdateBankTransferTreasuryVoucherViewModel>, IBankTransferTreasuryVoucherService
    {
        private readonly IVendorService _vendorService;
        private readonly ICustomerService _customerService;
        private readonly IEmployeeService _employeeService;
        private readonly IBankService _bankService;
        private readonly IBankAccountService _bankAccountService;
        private readonly IExpensesService _expensesService;
        private readonly IBillService _billService;
        private readonly IBillReturnService _billReturnService;
        private readonly IInvoiceService _invoiceService;
        private readonly IInvoiceReturnService _invoiceReturnService;

        public BankTransferTreasuryVoucherService(
            LocalSession session,
            IHttpClientFactory httpClientFactory,
            IVendorService vendorService,
            ICustomerService customerService,
            IEmployeeService employeeService,
            IBankService bankService,
            IBankAccountService bankAccountService,
            IExpensesService expensesService,
            IBillService billService,
            IBillReturnService billReturnService,
            IInvoiceService invoiceService,
            IInvoiceReturnService invoiceReturnService) : base(session, httpClientFactory)
        {
            _vendorService = vendorService;
            _customerService = customerService;
            _employeeService = employeeService;
            _bankService = bankService;
            _bankAccountService = bankAccountService;
            _expensesService = expensesService;
            _billService = billService;
            _billReturnService = billReturnService;
            _invoiceService = invoiceService;
            _invoiceReturnService = invoiceReturnService;
        }

        public async Task<ServiceResult<IEnumerable<BeneficiaryTypeEnumeration>>> SelectiveBeneficiaryTypeListAsync() => await Task.FromResult(BeneficiaryTypeEnumeration.BeneficiaryTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorListAsync() => await _vendorService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerListAsync() => await _customerService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveEmployeeListAsync() => await _employeeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TransactionTypeEnumeration>>> SelectiveTransactionTypeListAsync() => await Task.FromResult(TransactionTypeEnumeration.TreasuryBankTransactionTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBankListAsync() => await _bankService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<BankAccountModel>>> SelectiveBankAccountListAsync() => await _bankAccountService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<TreasuryLineTypeEnumeration>>> SelectiveTreasuryLineTypeListAsync() => await Task.FromResult(TreasuryLineTypeEnumeration.TreasuryLineTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveExpenseListAsync() => await _expensesService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillListAsync() => await _billService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillReturnListAsync() => await _billReturnService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceListAsync() => await _invoiceService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceReturnListAsync() => await _invoiceReturnService.GetSelectListAsync();

        [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecksPaginationList), HttpMethodEnum.GET)]
        public async Task<ServiceResult<PaginationList<BankTransferTreasuryVoucherModel>>> GetAllTreasuryVoucherChecksPaginationList(int pageNumber, int pageSize = 25)
        {
            var result = await _httpClient.SendRequest<ServiceResult<PaginationList<BankTransferTreasuryVoucherModel>>>(this, new { pageNumber, pageSize });
            if (result == null || result.Data == null)
                return ServiceResult<PaginationList<BankTransferTreasuryVoucherModel>>.Failure("No treasury vouchers found.");
            return result;
        }

        [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecks), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<BankTransferTreasuryVoucherModel>>> GetAllTreasuryVoucherChecks(Ulid? checkStatusId)
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<BankTransferTreasuryVoucherModel>>>(this, new { checkStatusId }) ?? new List<BankTransferTreasuryVoucherModel>();
            return result;
        }

        [HttpRequestMethod(nameof(GetTreasuryVoucherChecks), HttpMethodEnum.GET)]
        public async Task<ServiceResult<BankTransferTreasuryVoucherModel?>> GetTreasuryVoucherChecks(string checkId)
        {
            var result = await _httpClient.SendRequest<ServiceResult<BankTransferTreasuryVoucherModel?>>(this, new { checkId });
            if (result == null || result.Data == null)
                return ServiceResult<BankTransferTreasuryVoucherModel?>.Failure(checkId + " not found.");
            return result;
        }

        [HttpRequestMethod(nameof(SelectiveBankTransferTreasuryVoucherDtoListAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<IEnumerable<TreasuryVoucherDto>>> SelectiveBankTransferTreasuryVoucherDtoListAsync()
        {
            var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<TreasuryVoucherDto>>>(this) ?? new List<TreasuryVoucherDto>();
            return result;
        }

        [HttpRequestMethod(nameof(GetByIdJsonAsync), HttpMethodEnum.GET)]
        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            string result = await _httpClient.SendRequest<ServiceResult<string>>(this, new { id }) ?? string.Empty;
            return result;
        }
    }
}
