﻿namespace SimpleBooks.Services.Server.Business.Treasury.CashManagement
{
    public class CashTreasuryVoucherService : SimpleBooksBaseService<CashTreasuryVoucherModel, CashTreasuryVoucherModel, CreateCashTreasuryVoucherViewModel, UpdateCashTreasuryVoucherViewModel>, ICashTreasuryVoucherService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorService _vendorService;
        private readonly ICustomerService _customerService;
        private readonly IEmployeeService _employeeService;
        private readonly IDrawerService _drawerService;
        private readonly IDrawerLocationService _drawerLocationService;
        private readonly IExpensesService _expensesService;
        private readonly IBillService _billService;
        private readonly IBillReturnService _billReturnService;
        private readonly IInvoiceService _invoiceService;
        private readonly IInvoiceReturnService _invoiceReturnService;

        public CashTreasuryVoucherService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            IVendorService vendorService,
            ICustomerService customerService,
            IEmployeeService employeeService,
            IBankService bankService,
            IBankAccountService bankAccountService,
            ICheckVaultService checkVaultService,
            ICheckVaultLocationService checkVaultLocationService,
            IDrawerService drawerService,
            IDrawerLocationService drawerLocationService,
            IExpensesService expensesService,
            IBillService billService,
            IBillReturnService billReturnService,
            IInvoiceService invoiceService,
            IInvoiceReturnService invoiceReturnService) : base(serviceProvider, unitOfWork.CashTreasuryVoucher)
        {
            _unitOfWork = unitOfWork;
            _vendorService = vendorService;
            _customerService = customerService;
            _employeeService = employeeService;
            _drawerService = drawerService;
            _drawerLocationService = drawerLocationService;
            _expensesService = expensesService;
            _billService = billService;
            _billReturnService = billReturnService;
            _invoiceService = invoiceService;
            _invoiceReturnService = invoiceReturnService;
        }

        protected override Func<IQueryable<CashTreasuryVoucherModel>, IIncludableQueryable<CashTreasuryVoucherModel, object>>? Includes =>
            x => x
            .Include(xx => xx.TreasuryLines);

        public override void EditModelBeforeSave(CashTreasuryVoucherModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var treasuryLine in model.TreasuryLines)
            {
                treasuryLine.CashTreasuryVoucherId = model.Id;
                treasuryLine.CheckTreasuryVoucherId = null;
                treasuryLine.BankTransferTreasuryVoucherId = null;
                if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Expenses.Value)
                {
                    treasuryLine.BillId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Bill.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.BillReturn.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillId = null;
                    treasuryLine.InvoiceId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Invoice.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.InvoiceReturn.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceId = null;
                }
            }
            if (model.VoucherSerial == 0)
                model.VoucherSerial = _unitOfWork.CashTreasuryVoucher.GetNextVoucherSerial(model.TransactionTypeId);
            if (model.TVID == 0)
                model.TVID = _unitOfWork.CashTreasuryVoucher.GetNextTVID();
            if (model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value)
            {
                model.CustomerId = null;
                model.EmployeeId = null;
            }
            else if (model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value)
            {
                model.VendorId = null;
                model.EmployeeId = null;
            }
            else if (model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value)
            {
                model.VendorId = null;
                model.CustomerId = null;
            }
        }

        public override void ValidateEntity(CashTreasuryVoucherModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.TreasuryLines.Count == 0)
                throw new ValidationException("Treasury lines cannot be empty.");
            if (model.Amount != model.TreasuryLines.Sum(x => x.Amount))
                throw new ValidationException("The total amount of treasury lines must match the voucher amount.");
            if (model.TreasuryLines.Any(x => x.Amount <= 0))
                throw new ValidationException("Treasury line amounts must be greater than zero.");
            if (model.Amount <= 0)
                throw new ValidationException("Voucher amount must be greater than zero.");
            if (model.VendorId is null &&
                model.CustomerId is null &&
                model.EmployeeId is null)
                throw new ValidationException("At least one beneficiary must be selected.");
            foreach (var treasuryLine in model.TreasuryLines)
                if (treasuryLine.ExpensesId is null &&
                    treasuryLine.BillId is null &&
                    treasuryLine.BillReturnId is null &&
                    treasuryLine.InvoiceId is null &&
                    treasuryLine.InvoiceReturnId is null)
                    throw new ValidationException("Each treasury line must have at least one of Expenses, Bill, Bill Return, Invoice, or Invoice Return selected.");
        }

        public override CashTreasuryVoucherModel SetEntity(CashTreasuryVoucherModel model, CashTreasuryVoucherModel entity)
        {
            int serial = entity.VoucherSerial;
            int tvid = entity.TVID;

            entity = base.SetEntity(model, entity);

            entity.VoucherSerial = serial;
            entity.TVID = tvid;
            return entity;
        }

        public async Task<ServiceResult<IEnumerable<BeneficiaryTypeEnumeration>>> SelectiveBeneficiaryTypeListAsync() => await Task.FromResult(BeneficiaryTypeEnumeration.BeneficiaryTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorListAsync() => await _vendorService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerListAsync() => await _customerService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveEmployeeListAsync() => await _employeeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TransactionTypeEnumeration>>> SelectiveTransactionTypeListAsync() => await Task.FromResult(TransactionTypeEnumeration.TreasuryCashTransactionTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveDrawerListAsync() => await _drawerService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<DrawerLocationModel>>> SelectiveDrawerLocationListAsync() => await _drawerLocationService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<TreasuryLineTypeEnumeration>>> SelectiveTreasuryLineTypeListAsync() => await Task.FromResult(TreasuryLineTypeEnumeration.TreasuryLineTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveExpenseListAsync() => await _expensesService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillListAsync() => await _billService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillReturnListAsync() => await _billReturnService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceListAsync() => await _invoiceService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceReturnListAsync() => await _invoiceReturnService.GetSelectListAsync();

        public async Task<ServiceResult<IEnumerable<TreasuryVoucherDto>>> SelectiveCashTreasuryVoucherDtoListAsync() => (await GetAllAsync()).Data?.Select(x => new TreasuryVoucherDto()
        {
            Id = x.Id,
            TransactionSerial = x.VoucherSerial,
            BeneficiaryType = BeneficiaryTypeEnumeration.FromValue(x.BeneficiaryTypeId).Name,
            TreasuryVoucherJson = JsonConvert.SerializeObject(x),
        }).ToList() ?? new List<TreasuryVoucherDto>();

        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            RepositorySpecifications<CashTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CashTreasuryVoucherModel>()
            {
                SearchValue = x => x.Id == id,
                IsTackable = false,
            };
            if (Includes is not null)
                repositorySpecifications.Includes = Includes;
            var model = await _unitOfWork.CashTreasuryVoucher.GetAsync(repositorySpecifications);
            if (model is null)
                return string.Empty;

            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                PreserveReferencesHandling = PreserveReferencesHandling.Objects,
            };
            string json = JsonConvert.SerializeObject(model, jsonSerializerSettings);

            return json;
        }
    }
}
