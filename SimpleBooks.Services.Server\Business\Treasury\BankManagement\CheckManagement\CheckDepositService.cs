﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckDepositService : SimpleBooksBaseService<CheckDepositModel, CheckDepositModel, CreateCheckDepositViewModel, UpdateCheckDepositViewModel>, ICheckDepositService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBankService _bankService;
        private readonly IBankAccountService _bankAccountService;
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckDepositService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            IBankService bankService,
            IBankAccountService bankAccountService,
            ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(serviceProvider, unitOfWork.CheckDeposit)
        {
            _unitOfWork = unitOfWork;
            _bankService = bankService;
            _bankAccountService = bankAccountService;
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        protected override Func<IQueryable<CheckDepositModel>, IIncludableQueryable<CheckDepositModel, object>>? Includes =>
            x => x
            .Include(xx => xx.CheckTreasuryVouchers)
            .Include(xx => xx.CheckStatusHistories);

        public override async void ValidateEntity(CheckDepositModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.CheckTreasuryVouchers == null || !model.CheckTreasuryVouchers.Any())
            {
                throw new ValidationException("At least one check treasury voucher must be provided for deposit.");
            }
            var checksIds = model.CheckTreasuryVouchers.Select(x => x.Id);
            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>()
            {
                SearchValue = x => checksIds.Contains(x.Id),
            };
            var checkTreasuryVouchers = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecifications);
            foreach (var checkTreasuryVoucher in checkTreasuryVouchers)
            {
                if (checkTreasuryVoucher.CheckStatusId != CheckStatusEnumeration.Received.Value)
                {
                    throw new ValidationException("All check treasury vouchers must be in 'Received' status for deposit.");
                }
            }
        }

        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBankListAsync() => await _bankService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<BankAccountModel>>> SelectiveBankAccountListAsync() => await _bankAccountService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers() => await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Received.Value);
    }
}
