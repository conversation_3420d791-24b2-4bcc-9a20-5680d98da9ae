﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class ProductService : SimpleBooksBaseService<ProductModel, IndexProductViewModel, CreateProductViewModel, UpdateProductViewModel>, IProductService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IProductCategoryService _productCategoryService;
        private readonly IUnitService _unitService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public ProductService(
            IServiceProvider serviceProvider,
            IUnitOfWork unitOfWork,
            IProductCategoryService productCategoryService,
            IUnitService unitService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(serviceProvider, unitOfWork.Product)
        {
            _unitOfWork = unitOfWork;
            _productCategoryService = productCategoryService;
            _unitService = unitService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        protected override Func<IQueryable<ProductModel>, IIncludableQueryable<ProductModel, object>>? Includes =>
            x => x
            .Include(xx => xx.ProductUnits).ThenInclude(xx => xx.ProductUnit)
            .Include(xx => xx.ProductTaxes);

        public override void EditModelBeforeSave(ProductModel model)
        {
            base.EditModelBeforeSave(model);

            if (model.ProductCategoryId == Ulid.Empty)
                model.ProductCategoryId = null;
            if (model.ProductTypeId == Ulid.Empty)
                model.ProductTypeId = null;
        }

        public override void ValidateEntity(ProductModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (isEdit == false)
            {
                RepositorySpecifications<ProductModel> repositorySpecifications = new RepositorySpecifications<ProductModel>()
                {
                    SearchValue = x => x.ProductId == model.ProductId,
                    IsTackable = false,
                };
                var isExistingProduct = _unitOfWork.Product.Get(repositorySpecifications);
                if (isExistingProduct != null && isExistingProduct.Id != model.Id)
                    throw new ValidationException("Product with the same Product ID already exists.");
            }
            if (model.ProductUnits.Count >= 1)
            {
                var hasDuplicates = model.ProductUnits.GroupBy(x => x.ProductUnitId).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Product units must be unique.");
                var hasInvalidUnitRatio = model.ProductUnits.Any(x => x.ProductUnitRatio <= 0);
                if (hasInvalidUnitRatio)
                    throw new ValidationException("Product unit ratio must be greater than zero.");
                var baseRatioUnits = model.ProductUnits.Where(x => x.ProductUnitRatio == 1).ToList();
                if (baseRatioUnits.Count > 1)
                    throw new ValidationException("Only one product unit can have a ratio of 1.");
                else if (baseRatioUnits.Count == 0)
                    throw new ValidationException("At least one product unit must have a ratio of 1.");
            }
            if (model.ProductTaxes.Count >= 1)
            {
                var hasDuplicates = model.ProductTaxes.GroupBy(x => x.TaxSubTypeId).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Product taxes must be unique.");
                var hasInvalidTaxRatio = model.ProductTaxes.Any(x => x.ProductTaxsRatio < 0);
                if (hasInvalidTaxRatio)
                    throw new ValidationException("Product tax ratio must be greater than or equal to zero.");
            }
        }

        public async Task<ServiceResult<IEnumerable<ProductTypeEnumeration>>> SelectiveProductTypeListAsync() => await Task.FromResult(ProductTypeEnumeration.ProductTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveProductCategoryListAsync() => await _productCategoryService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();
    }
}
