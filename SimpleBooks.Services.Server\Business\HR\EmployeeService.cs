﻿namespace SimpleBooks.Services.Server.Business.HR
{
    public class EmployeeService : SimpleBooksBaseService<EmployeeModel, EmployeeModel, CreateEmployeeViewModel, UpdateEmployeeViewModel>, IEmployeeService
    {
        private readonly IUnitOfWork _unitOfWork;

        public EmployeeService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.Employee)
        {
            _unitOfWork = unitOfWork;
        }

        public override void EditModelBeforeSave(EmployeeModel model)
        {
            base.EditModelBeforeSave(model);

            if (model.UserId == Ulid.Empty)
                model.UserId = null;
        }

        public override void ValidateEntity(EmployeeModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);
            if (isEdit == false)
            {
                RepositorySpecifications<EmployeeModel> repositorySpecifications = new RepositorySpecifications<EmployeeModel>()
                {
                    SearchValue = x => x.EmployeeName == model.EmployeeName || x.EmployeeEMail == model.EmployeeEMail || x.EmployeePhone == model.EmployeePhone,
                    IsTackable = false,
                };
                var isExistingEmployee = _unitOfWork.Employee.Get(repositorySpecifications);
                if (isExistingEmployee != null && isExistingEmployee.Id != model.Id)
                    throw new ValidationException("Employee with the same Name or e-mail or phone already exists.");
            }
        }

        public async Task<ServiceResult<IEnumerable<IdAndName>>> GetRepEmployeeSelectListAsync()
        {
            return await _unitOfWork.Employee.GetAsSelectedItemsAsync(new RepositorySpecifications<EmployeeModel>()
            {
                SearchValue = x => x.EmployeeIsRep,
            });
        }
    }
}
