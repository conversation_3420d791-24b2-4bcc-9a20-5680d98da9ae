﻿namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultService : SimpleBooksBaseService<CheckVaultModel, CheckVaultModel, CreateCheckVaultViewModel, UpdateCheckVaultViewModel>, ICheckVaultService
    {
        public CheckVaultService(LocalSession session, IHttpClientFactory httpClientFactory) : base(session, httpClientFactory)
        {
        }
    }
}
