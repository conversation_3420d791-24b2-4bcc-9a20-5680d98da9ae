﻿namespace SimpleBooks.Web.Middlewares
{
    public class TokenMiddleware
    {
        private readonly RequestDelegate _next;
        private const string AuthHeader = AuthenticationDefaults.AuthenticationHeader;
        private const string AuthTokenCookie = AuthenticationDefaults.AuthenticationCookie;
        private const string RefreshTokenCookie = AuthenticationDefaults.RefreshTokenCookie;

        public TokenMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.Request.Path != "/Authentication/Login" && context.Request.Path != "/Authentication/UnAuthorizedIndex")
            {
                string token = context.GetTokenString();
                if (!string.IsNullOrEmpty(token))
                    await HandleAuthTokenAsync(context, token);
                else
                    await HandleRefreshTokenAsync(context);
            }

            await _next(context);

            if (context.Response.StatusCode == 401)
                HandleUnauthorizedResponse(context);
        }

        private async Task HandleAuthTokenAsync(HttpContext context, string token)
        {
            var accessToken = token.ToString().Replace(AuthenticationDefaults.AuthenticationScheme + " ", "");
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadToken(accessToken) as JwtSecurityToken;

            if (jwtToken != null && jwtToken.ValidTo < DateTime.UtcNow)
            {
                context.Response.Cookies.Delete(AuthTokenCookie);
                await HandleRefreshTokenAsync(context);
            }
            else
            {
                context.Request.Headers[AuthHeader] = $"Bearer {accessToken}";
                if (context.Request.Path == "/Authentication/Login")
                    context.Response.Redirect("/Home/Index");
            }

            await Task.CompletedTask;
        }

        private async Task HandleRefreshTokenAsync(HttpContext context)
        {
            if (context.Request.Cookies.TryGetValue(RefreshTokenCookie, out var refreshToken) && !string.IsNullOrEmpty(refreshToken))
            {
                try
                {
                    RefreshTokenRequest refreshTokenRequest = new RefreshTokenRequest() { Token = refreshToken };
                    using var scope = context.RequestServices.CreateScope();
                    var authenticationService = scope.ServiceProvider.GetRequiredService<IAuthenticationService>();
                    var newAccessToken = await authenticationService.RefreshTokenAsync(refreshTokenRequest).GetDataOrThrowIfNullAsync();
                    context.Response.Cookies.SetAuthenticationCookies(newAccessToken);
                    if (newAccessToken is not null && !string.IsNullOrEmpty(newAccessToken.Token))
                    {
                        context.Request.Headers[AuthHeader] = $"Bearer {newAccessToken.Token}";
                    }
                    else
                    {
                        context.Response.Cookies.Delete(RefreshTokenCookie);
                        context.Response.Redirect("/Authentication/Login");
                    }
                }
                catch
                {
                    context.Response.Redirect("/Authentication/Login");
                }
            }
            else
                context.Response.Redirect("/Authentication/Login");
        }

        private void HandleUnauthorizedResponse(HttpContext context)
        {
            context.Response.Redirect("/Authentication/UnAuthorizedIndex");
        }
    }
}
