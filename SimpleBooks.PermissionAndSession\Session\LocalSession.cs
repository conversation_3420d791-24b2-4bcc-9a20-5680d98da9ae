﻿namespace SimpleBooks.PermissionAndSession.Session
{
    public static class LocalSessionStatic
    {
        public static LoginSessionResponse LoginSession { get; private set; }

        public static void SetEmployee(LoginSessionResponse loginSession)
        {
            LoginSession = loginSession;
            IBaseUserModel baseUser = new BaseUserModel()
            {
                Id = loginSession.Employee.Id,
                UserName = loginSession.Employee.User.UserName,
                UserTypeId = loginSession.Employee.User.UserTypeId,
            };
            BaseQueryBuilder.User = baseUser;
            BaseSession.SetUser(baseUser, Screens.GetScreens, loginSession.Employee.User.ScreensAccessProfile);
        }
    }

    public class LocalSession 
    {
        public LocalSession()
        {
            AppLogger = new SerilogLogging(SessionPaths.LoggerPath());
        }

        public ILogging AppLogger { get; }
        public LoginSessionResponse? LoginSessionResponse => LocalSessionStatic.LoginSession;
        public EmployeeLoginSessionResponse Employee => LoginSessionResponse?.Employee ?? throw new NullReferenceException("Employee must not be null");
        public SettingLoginSessionResponse Setting => LoginSessionResponse?.Employee?.Setting ?? throw new NullReferenceException("Setting must not be null");
        public List<BaseScreensAccessTemplate> ScreensAccesses => BaseSession.ScreensAccesses;

        public void SetEmployee(LoginSessionResponse loginSession) => LocalSessionStatic.SetEmployee(loginSession);
    }
}
