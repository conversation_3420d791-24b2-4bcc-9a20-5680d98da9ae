﻿namespace SimpleBooks.Services.Server.Business.Warehouse
{
    public class InventoryTaxService : SimpleBooksBaseService<InventoryTaxModel, InventoryTaxModel, CreateInventoryTaxViewModel, UpdateInventoryTaxViewModel>, IInventoryTaxService
    {
        private readonly IUnitOfWork _unitOfWork;

        public InventoryTaxService(IServiceProvider serviceProvider, IUnitOfWork unitOfWork) : base(serviceProvider, unitOfWork.InventoryTax)
        {
            _unitOfWork = unitOfWork;
        }
    }
}
